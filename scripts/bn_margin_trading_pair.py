import requests
import csv
import os

# 设置代理
PROXIES = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890'
}

def get_futures_base_assets():
    """Get base assets that have perpetual futures contracts"""
    url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
    try:
        print("Fetching futures symbols...")
        r = requests.get(url, proxies=PROXIES, timeout=10)
        r.raise_for_status()
        data = r.json()
        futures_base_assets = set()
        for s in data['symbols']:
            if s['status'] == 'TRADING' and s['contractType'] == 'PERPETUAL':
                # Convert BTCUSDT futures to spot format for comparison
                # 提取base asset，比如从BTCUSDT提取BTC
                futures_base_assets.add(s['baseAsset'])
        print(f"Successfully fetched {len(futures_base_assets)} futures base assets")
        return futures_base_assets
    except requests.exceptions.RequestException as e:
        print(f"Error fetching futures symbols: {e}")
        return set()

def get_spot_symbols():
    url = "https://api.binance.com/api/v3/exchangeInfo"
    try:
        print("Fetching spot symbols...")
        r = requests.get(url, proxies=PROXIES, timeout=10)
        r.raise_for_status()
        data = r.json()
        result = {}
        for s in data['symbols']:
            if s['status'] == 'TRADING' and s['isspTradingAllowed']:
                price_tick = 0
                lot_size = 0
                min_order_qty = 0
                min_national = 0
                for filt in s['filters']:
                    if filt['filterType'] == 'PRICE_FILTER':
                        price_tick = filt['tickSize']
                    if filt['filterType'] == 'LOT_SIZE':
                        lot_size = filt['stepSize']
                        min_order_qty = filt['minQty']
                    if filt['filterType'] == 'NOTIONAL':
                        min_national = filt['minNotional']
                result[s['symbol']] = (
                    s['quoteAsset'],
                    s['baseAsset'],
                    price_tick,
                    lot_size,
                    min_order_qty,
                    min_national
                )
        print(f"Successfully fetched {len(result)} spot margin symbols")
        return result
    except requests.exceptions.RequestException as e:
        print(f"Error fetching spot symbols: {e}")
        return {}

def get_ticker_data():
    url = "https://api.binance.com/api/v3/ticker/24hr"
    try:
        print("Fetching ticker data...")
        r = requests.get(url, proxies=PROXIES, timeout=10)
        r.raise_for_status()
        data = r.json()
        print(f"Successfully fetched ticker data for {len(data)} symbols")
        return data
    except requests.exceptions.RequestException as e:
        print(f"Error fetching ticker data: {e}")
        return []

def get_usd_prices():
    """Fetch USD price for major quote assets using USDT/BUSD pairs"""
    url = "https://api.binance.com/api/v3/ticker/price"
    try:
        print("Fetching USD price data...")
        r = requests.get(url, proxies=PROXIES, timeout=10)
        r.raise_for_status()
        prices = {item['symbol']: float(item['price']) for item in r.json()}
    except requests.exceptions.RequestException as e:
        print(f"Error fetching USD prices: {e}")
        prices = {}

    # USDT/BUSD considered USD stablecoins
    price_map = {
        'USDT': 1.0,
        'BUSD': 1.0,
        'FDUSD': 1.0,
        'TUSD': 1.0,
        'USDC': 1.0,
    }

    # Get other quote asset to USDT (like BTCUSDT → BTC)
    for symbol, price in prices.items():
        if symbol.endswith('USDT'):
            asset = symbol.replace('USDT', '')
            price_map[asset] = price
        if symbol.endswith('BUSD'):
            asset = symbol.replace('BUSD', '')
            price_map[asset] = price
        if symbol.endswith('FDUSD'):
            asset = symbol.replace('FDUSD', '')
            price_map[asset] = price
        if symbol.endswith('TUSD'):
            asset = symbol.replace('TUSD', '')
            price_map[asset] = price
        if symbol.endswith('USDC'):
            asset = symbol.replace('USDC', '')
            price_map[asset] = price


    return price_map

def calculate_usd_market_values():
    spot_symbols = get_spot_symbols()
    futures_base_assets = get_futures_base_assets()
    tickers = get_ticker_data()
    usd_rates = get_usd_prices()

    # Filter to only include spot symbols whose base asset has futures contracts
    hedgeable_symbols = {}
    for symbol, symbol_data in spot_symbols.items():
        quote_asset, base_asset, price_tick, lot_size, min_order_qty, min_national = symbol_data
        if base_asset in futures_base_assets:
            hedgeable_symbols[symbol] = symbol_data

    print(f"Found {len(spot_symbols)} spot margin symbols")
    print(f"Found {len(futures_base_assets)} unique base assets with futures contracts")
    print(f"Found {len(hedgeable_symbols)} spot margin symbols that can be hedged with futures")
    print()

    results = []
    for ticker in tickers:
        symbol = ticker['symbol']
        if symbol not in hedgeable_symbols:
            continue

        quote_asset, base_asset, price_tick, lot_size, min_order_qty, min_national = hedgeable_symbols[symbol]
        quote_volume = float(ticker['quoteVolume'])

        quote_to_usd = usd_rates.get(quote_asset)
        if not quote_to_usd:
            continue  # skip unknown assets

        usd_volume = quote_volume * quote_to_usd
        results.append((symbol, usd_volume, quote_asset, quote_volume, base_asset, price_tick, lot_size, min_order_qty, min_national))

    results.sort(key=lambda x: x[1], reverse=True)
    return results

def write_to_csv(data, filename='bn_margin_trading_pair_with_vol_in_usd.csv'):
    with open(filename, mode='w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Rank', 'Symbol', 'USD Volume', 'Quote Asset', 'Quote Volume', 'Base Asset', 'Price Tick', 'Lot Size', 'Min Order Qty', 'Min Notional'])
        for i, (symbol, usd_value, quote_asset, quote_volume, base_asset, price_tick, lot_size, min_order_qty, min_national) in enumerate(data, start=1):
            writer.writerow([i, symbol, f"{usd_value:.2f}", quote_asset, f"{quote_volume:.2f}", base_asset, price_tick, lot_size, min_order_qty, min_national])
    print(f"\n✅ Results written to '{filename}'")

def main():
    mv_list = calculate_usd_market_values()
    print(f"{'Rank':<5} {'Symbol':<12} {'USD Volume':>20} {'QuoteAsset':>12} {'QuoteVolume':>20} {'BaseAsset':>12} {'PriceTick':>12} {'LotSize':>12} {'MinOrderQty':>12} {'MinNotional':>12}")
    print("-" * 80)
    for i, (symbol, usd_value, quote_asset, quote_volume, base_asset, price_tick, lot_size, min_order_qty, min_national) in enumerate(mv_list[:200], start=1):
        print(f"{i:<5} {symbol:<12} {usd_value:>20,.2f} {quote_asset:>12} {quote_volume:>20,.2f} {base_asset:>12} {price_tick:>12} {lot_size:>12} {min_order_qty:>12} {min_national:>12}")
    write_to_csv(mv_list)

if __name__ == "__main__":
    main()
