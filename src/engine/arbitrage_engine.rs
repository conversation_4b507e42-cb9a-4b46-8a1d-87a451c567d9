use crate::{
    Currency, EdgeDirection, ORDER_FILTER_LOT_SIZE_INDEX, ORDER_FILTER_MIN_ORDER_QTY_INDEX,
    ORDER_FILTERS, ORDER_QUANTITIES, PREDEFINED_RINGS, TRADING_PAIR_RATES,
    TRADING_PAIR_TO_RING_INDEX, TradingPair, WebSocketHandle,
    encoding::{
        agg_trades::AggTrade,
        book_ticker::BookTicker,
        depth_snapshot::DepthSnapshot,
        depth_update::DepthUpdate,
        sbe::{SbeBookTicker, SbeDepthSnapshot, SbeDepthUpdate, SbeTrade},
    },
    engine::{
        monitor::CURRENT_RING_TS,
        token::{ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, ORDER_TOKEN_4},
        trade::generate_order_requests,
        trading_pair::{
            ORDER_BOOKS, TRADING_PAIR_RATE_EVENT_TIME, TRADING_PAIR_RATE_FROM,
            TRADING_PAIR_RATE_UPDATE_ID, TRADING_PAIR_RATE_UPDATE_TIME,
        },
    },
    logln,
    utils::{
        self,
        perf::{circles_to_ns, system_now_in_us},
    },
};

// BNB 折扣率 (75% 折扣，即支付原价的 75%)
// const BNB_DISCOUNT: f64 = 0.75;

fn adjust_quantity(qty: f64, min_qty: f64, step: f64) -> Option<f64> {
    if qty < min_qty {
        return None;
    }
    let steps = (qty / step).floor();
    let adjusted = steps * step;
    if adjusted >= min_qty {
        Some(adjusted)
    } else {
        let min_multiple = (min_qty / step).ceil() * step;
        if min_multiple > qty {
            None
        } else {
            Some(min_multiple)
        }
    }
}

fn quote_to_base_quantity(quote: f64, pair: TradingPair) -> f64 {
    unsafe { quote * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] }
}

fn base_to_quote_quantity(base: f64, pair: TradingPair) -> f64 {
    unsafe { base * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] }
}

pub static mut USER_DATA_SUBSCRIBED: bool = false;
pub static mut TRADING_FEE_UPDATED: bool = false;

pub struct ArbitrageEngine {}

impl ArbitrageEngine {
    pub fn set_user_data_subscribed() {
        unsafe {
            USER_DATA_SUBSCRIBED = true;
        }
    }

    pub fn user_data_subscribed() -> bool {
        unsafe { USER_DATA_SUBSCRIBED }
    }

    pub fn set_trading_fee_updated() {
        unsafe {
            TRADING_FEE_UPDATED = true;
        }
    }

    pub fn get_trading_fee_updated() -> bool {
        unsafe { TRADING_FEE_UPDATED }
    }

    #[inline(always)]
    pub fn check_arbitrage_conditions(now: u64) -> bool {
        unsafe {
            USER_DATA_SUBSCRIBED && TRADING_FEE_UPDATED && {
                circles_to_ns(now - CURRENT_RING_TS) > 200_000_000.0
            }
        }
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn place_orders<const IN_LEN: usize, const OUT_LEN: usize>(
        ring_index: usize,
        now: u64,
        handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
    ) {
        let ring = PREDEFINED_RINGS[ring_index];
        let len = ring.len();

        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        match len {
            3 => {
                let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 0);
                let buf = handle.get_write_buf(ORDER_TOKEN_2).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 1);
                let buf = handle.get_write_buf(ORDER_TOKEN_3).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 2);
                handle
                    .trigger_write_3(ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3)
                    .unwrap();
            }
            4 => {
                let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 0);
                let buf = handle.get_write_buf(ORDER_TOKEN_2).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 1);
                let buf = handle.get_write_buf(ORDER_TOKEN_3).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 2);
                let buf = handle.get_write_buf(ORDER_TOKEN_4).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 3);

                handle
                    .trigger_write_4(ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, ORDER_TOKEN_4)
                    .unwrap();
            }
            _ => {}
        }
        let end = utils::perf::now();
        let mut buf = ryu::Buffer::new();
        let latency_str = buf.format(circles_to_ns(end - now));
        logln!("Total latency: {}ns", latency_str);
        unsafe {
            CURRENT_RING_TS = now;
        }
        let mut product = 1.0;
        for i in 0..PREDEFINED_RINGS[ring_index].len() {
            let (pair, dir) = PREDEFINED_RINGS[ring_index][i];
            let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
            product *= rate;
            let price = match dir {
                EdgeDirection::Forward => 1.0 / rate,
                EdgeDirection::Reverse => rate,
            };
            let mut price_buffer = ryu::Buffer::new();
            let price_str = price_buffer.format(price);
            let mut update_time_buffer = itoa::Buffer::new();
            let update_time_str =
                update_time_buffer.format(unsafe { TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] });
            let mut event_time_buffer = itoa::Buffer::new();
            let event_time_str =
                event_time_buffer.format(unsafe { TRADING_PAIR_RATE_EVENT_TIME[pair as usize] });
            let mut from_buffer = itoa::Buffer::new();
            let from_str = from_buffer.format(unsafe { TRADING_PAIR_RATE_FROM[pair as usize] });
            logln!(
                "{} p: {} dir: {} u_ts: {} e_ts: {} from: {} ",
                pair.to_str(),
                dir.to_str(),
                price_str,
                update_time_str,
                event_time_str,
                from_str
            );
            let depth = unsafe { ORDER_BOOKS[pair as usize] };
            logln!("Depth: {}", depth);
        }
        let mut ryu_buffer = ryu::Buffer::new();
        let max_product_str = ryu_buffer.format(product);
        logln!("Expect rate: {}", max_product_str);
    }

    pub fn update_rate_by_ws_book_ticker(book_ticker: &BookTicker) -> bool {
        let pair: TradingPair = book_ticker.symbol.into();
        unsafe {
            // 如果已经由trade标记为无效，则暂时不用json bbo了
            if TRADING_PAIR_RATE_FROM[pair as usize] == 0 {
                if book_ticker.update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                    TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.update_id;
                }
                return false;
            }
            if book_ticker.update_id <= TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                return false;
            }
            let now = system_now_in_us();
            TRADING_PAIR_RATE_FROM[pair as usize] = 4;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                1.0 / book_ticker.ask_price;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] =
                book_ticker.bid_price;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.update_id;
        }
        true
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn update_rate(book_ticker: &SbeBookTicker) -> bool {
        let pair: TradingPair = book_ticker.symbol.into();
        unsafe {
            if book_ticker.book_update_id <= TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                return false;
            }
            let now = system_now_in_us();
            if (now - book_ticker.event_time) > 700 {
                TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                if book_ticker.book_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                    TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.book_update_id;
                }
                return false;
            }
            TRADING_PAIR_RATE_FROM[pair as usize] = 1;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                1.0 / book_ticker.ask_price;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] =
                book_ticker.bid_price;
            TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = book_ticker.event_time;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = system_now_in_us();
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.book_update_id;
        }
        true
    }

    pub fn update_orderbook_by_updates(diff: &SbeDepthUpdate) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = diff.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_diff(
                diff.final_update_id,
                &diff.bid_updates,
                &diff.ask_updates,
                diff.event_time,
            ) {
                return false;
            }
            if diff.final_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - diff.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 2;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = diff.final_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = diff.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
        }
        return false;
    }

    pub fn update_orderbook_by_ws_updates(diff: &DepthUpdate) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = diff.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_diff(
                diff.final_update_id,
                &diff.bid_updates,
                &diff.ask_updates,
                diff.event_time,
            ) {
                return false;
            }
            if diff.final_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - diff.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 5;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = diff.final_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = diff.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
        }
        return false;
    }

    pub fn update_orderbook_by_snapshot(snapshot: &SbeDepthSnapshot) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = snapshot.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_snapshot(
                snapshot.last_update_id,
                &snapshot.bids,
                &snapshot.asks,
                snapshot.event_time,
            ) {
                return false;
            }
            if snapshot.last_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - snapshot.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 3;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = snapshot.last_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = snapshot.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
            false
        }
    }

    pub fn update_orderbook_by_ws_snapshot(snapshot: &DepthSnapshot) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = TradingPair::from_lower_case(snapshot.symbol);
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_snapshot(
                snapshot.last_update_id,
                &snapshot.bids,
                &snapshot.asks,
                0, // no event time
            ) {
                return false;
            }
            if snapshot.last_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                TRADING_PAIR_RATE_FROM[pair as usize] = 6;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = snapshot.last_update_id;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
            false
        }
    }

    pub fn update_rate_by_trades(trade: SbeTrade) {
        let pair: TradingPair = trade.symbol.into();
        // 无脑信任trade比bbo或者depth更快
        // trade last trade price 如果不在[bid1, asks1]之间，则标记为 invalid
        let price = trade.last_trade_price;
        unsafe {
            let bid1 = TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize];
            let ask1 = 1.0 / TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize];
            if !(price > bid1 && price < ask1) {
                TRADING_PAIR_RATE_FROM[pair as usize] = 0;
            }
            TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = trade.event_time;
        }
    }

    pub fn update_rate_by_agg_trades(trade: AggTrade) {
        let pair: TradingPair = trade.symbol.into();
        let update_time = unsafe { TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] };

        // 统一使用event_time对比
        if trade.event_time > update_time {
            // trade last trade priru 如果不在[bid1, asks1]之间，则标记为 invalid
            let price = trade.price;
            unsafe {
                let bid1 = TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize];
                let ask1 = 1.0 / TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize];
                if !(price > bid1 && price < ask1) {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                }
            }
        }
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn compute_orders(circle_index: usize) -> Option<usize> {
        let ring = PREDEFINED_RINGS[circle_index];
        for try_count in 0..100 {
            let mut init_amount: f64 = 0.0;
            let mut last_asset_q = 0.0f64;
            let mut i = 0;
            while i < ring.len() {
                let (pair, direction) = ring[i];
                let order_filters = ORDER_FILTERS[pair as usize];
                if i == 0 {
                    if try_count == 0 {
                        let init_q = match direction {
                            EdgeDirection::Forward => match pair.quote() {
                                Currency::XETH => 0.002f64,
                                // Currency::XBTC => 0.0001f64,
                                _ => 10.0f64,
                            },
                            EdgeDirection::Reverse => match pair.base() {
                                Currency::XETH => 0.002f64,
                                // Currency::XBTC => 0.0001f64,
                                _ => 1.0f64,
                            },
                        };
                        match direction {
                            EdgeDirection::Forward => unsafe {
                                let base_asset_q = quote_to_base_quantity(init_q, pair);
                                let base_asset_q_adjusted = adjust_quantity(
                                    base_asset_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                last_asset_q = ORDER_QUANTITIES[i];
                            },
                            EdgeDirection::Reverse => unsafe {
                                let base_asset_q_adjusted = adjust_quantity(
                                    init_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                last_asset_q = base_to_quote_quantity(base_asset_q_adjusted, pair);
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_asset_q_adjusted;
                            },
                        }
                    } else {
                        unsafe {
                            ORDER_QUANTITIES[i] += order_filters[ORDER_FILTER_LOT_SIZE_INDEX];
                            match direction {
                                EdgeDirection::Forward => {
                                    init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                    last_asset_q = ORDER_QUANTITIES[i];
                                }
                                EdgeDirection::Reverse => {
                                    init_amount = ORDER_QUANTITIES[i];
                                    last_asset_q =
                                        base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                }
                            }
                        }
                    }
                    i += 1;
                    continue;
                }
                let q = match direction {
                    EdgeDirection::Forward => quote_to_base_quantity(last_asset_q, pair),
                    EdgeDirection::Reverse => last_asset_q,
                };
                let q = match adjust_quantity(
                    q,
                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                ) {
                    Some(r) => r,
                    None => break,
                };
                unsafe {
                    ORDER_QUANTITIES[i] = q;
                }
                last_asset_q = match direction {
                    EdgeDirection::Forward => q,
                    EdgeDirection::Reverse => base_to_quote_quantity(q, pair),
                };
                i += 1;
            }
            if i < ring.len() {
                continue;
            }

            match ring[ring.len() - 1].1 {
                EdgeDirection::Forward => unsafe {
                    if ((ORDER_QUANTITIES[ring.len() - 1] - init_amount) / init_amount).abs() < 0.1
                    {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                },
                EdgeDirection::Reverse => {
                    if ((last_asset_q - init_amount) / init_amount).abs() < 0.1 {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                }
            }
        }
        None
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn check_arbitrage(now: u64, pair: TradingPair) -> Option<usize> {
        if !Self::check_arbitrage_conditions(now) {
            return None;
        }
        let ring_indices = TRADING_PAIR_TO_RING_INDEX[pair as usize];
        let mut max_product = 0.0f64;
        let mut result: usize = 0;
        let system_now = system_now_in_us();
        for index in ring_indices {
            let ring = PREDEFINED_RINGS[*index as usize];
            let mut product = 1.0;
            // let mut total_fee = 0.0;

            for &(pair, dir) in ring {
                if unsafe { TRADING_PAIR_RATE_FROM[pair as usize] } == 0 {
                    product = 0.0;
                    break;
                }
                let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
                if rate == 0.0 {
                    product = 0.0;
                    break;
                }
                let orderbook = unsafe { ORDER_BOOKS[pair as usize] };
                if (system_now - orderbook.event_time) < 80_000 {
                    match dir {
                        EdgeDirection::Forward => {
                            match orderbook.find_nearest_ask_level(1.0 / rate) {
                                Some(level) => {
                                    if level.0 > 2 {
                                        product = 0.0;
                                        break;
                                    }
                                    let ask_amount = orderbook.asks_amount_from(level.0);
                                    if ask_amount < 1200.0 {
                                        product = 0.0;
                                        break;
                                    }
                                    if pair.base() != Currency::XETH
                                        && orderbook.spread_in_bp() < 0.8
                                    {
                                        product = 0.0;
                                        break;
                                    }
                                }
                                None => {
                                    product = 0.0;
                                    break;
                                }
                            }
                        }
                        EdgeDirection::Reverse => match orderbook.find_nearest_bid_level(rate) {
                            Some(level) => {
                                if level.0 > 2 {
                                    product = 0.0;
                                    break;
                                }
                                let ask_amount = orderbook.bids_amount_from(level.0);
                                if ask_amount < 1200.0 {
                                    product = 0.0;
                                    break;
                                }
                                if pair.base() != Currency::XETH && orderbook.spread_in_bp() < 0.8 {
                                    product = 0.0;
                                    break;
                                }
                            }
                            None => {
                                product = 0.0;
                                break;
                            }
                        },
                    }
                } else {
                    product = 0.0;
                    break;
                }

                product *= rate;

                // 计算该交易对的实际手续费 (taker fee * BNB 折扣)
                // let taker_fee = unsafe { TRADING_FEES[pair as usize][TAKER_FEE_INDEX] };
                // let actual_fee = taker_fee * BNB_DISCOUNT;
                // total_fee += if taker_fee == 0.0 {
                //     0.0
                // } else {
                //     0.0001725 * 1.1
                // };
            }

            // let threshold = 1.0 + total_fee;
            if product > max_product {
                result = *index as usize;
                max_product = product;
            }
        }
        if max_product > 1.001 {
            Some(result)
        } else {
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn find_ring(pair1: TradingPair, pair2: TradingPair, pair3: TradingPair) -> Option<usize> {
        for i in 0..PREDEFINED_RINGS.len() {
            let ring = PREDEFINED_RINGS[i];
            if ring.len() >= 3 && ring[0].0 == pair1 && ring[1].0 == pair2 && ring[2].0 == pair3 {
                return Some(i);
            }
        }
        None
    }

    fn setup_test_rates() {
        // 清零所有汇率
        unsafe {
            use std::ptr::addr_of_mut;
            let rates_ptr = addr_of_mut!(TRADING_PAIR_RATES);
            for i in 0..28 {
                // 硬编码长度以避免引用静态变量
                (*rates_ptr)[i][EdgeDirection::Forward as usize] = 0.0;
                (*rates_ptr)[i][EdgeDirection::Reverse as usize] = 0.0;
            }
            // 清零订单数量
            let quantities_ptr = addr_of_mut!(ORDER_QUANTITIES);
            for i in 0..10 {
                // 硬编码长度以避免引用静态变量
                (*quantities_ptr)[i] = 0.0;
            }
        }
    }

    #[test]
    fn test_adjust_quantity_basic() {
        // 测试基本的数量调整功能
        assert_eq!(adjust_quantity(10.0, 5.0, 1.0), Some(10.0));
        assert_eq!(adjust_quantity(10.5, 5.0, 1.0), Some(10.0));
        assert_eq!(adjust_quantity(4.0, 5.0, 1.0), None);
        assert_eq!(adjust_quantity(5.0, 5.0, 1.0), Some(5.0));
    }

    #[test]
    fn test_adjust_quantity_step_size() {
        // 测试步长调整 - 使用浮点数比较来避免精度问题
        let result1 = adjust_quantity(10.25, 5.0, 0.1);
        assert!(result1.is_some());
        assert!((result1.unwrap() - 10.2).abs() < 1e-10);

        let result2 = adjust_quantity(10.05, 5.0, 0.1);
        assert!(result2.is_some());
        assert!((result2.unwrap() - 10.0).abs() < 1e-10);

        assert_eq!(adjust_quantity(4.95, 5.0, 0.1), None);
    }

    #[test]
    fn test_adjust_quantity_edge_cases() {
        // 测试边界情况

        // 1. 零值测试
        assert_eq!(adjust_quantity(0.0, 0.0, 1.0), Some(0.0));
        assert_eq!(adjust_quantity(0.0, 1.0, 1.0), None);

        // 2. 步长为零的情况 - 这可能导致除零错误或无限循环
        // 这是一个潜在的 bug！
        // assert_eq!(adjust_quantity(10.0, 5.0, 0.0), None); // 这会导致除零

        // 3. 负数测试
        assert_eq!(adjust_quantity(-10.0, 5.0, 1.0), None);
        assert_eq!(adjust_quantity(10.0, -5.0, 1.0), Some(10.0)); // 负的最小值？

        // 4. 非常小的步长
        let result = adjust_quantity(1.0, 0.5, 1e-10);
        assert!(result.is_some());

        // 5. 数量刚好等于最小值
        assert_eq!(adjust_quantity(5.0, 5.0, 1.0), Some(5.0));

        // 6. 数量略小于最小值但在一个步长内
        assert_eq!(adjust_quantity(4.9, 5.0, 1.0), None);

        // 7. 非常大的数值
        let large_qty = 1e15;
        let result = adjust_quantity(large_qty, 1e10, 1e5);
        assert!(result.is_some());
    }

    #[test]
    fn test_adjust_quantity_min_multiple_logic() {
        // 测试 min_multiple 逻辑中的潜在 bug

        // 当 qty < min_qty 时，函数会计算 min_multiple
        // 这里测试这个逻辑是否正确

        // 情况1: min_multiple 刚好等于 qty
        let result = adjust_quantity(5.0, 5.1, 0.1);
        // min_multiple = ceil(5.1 / 0.1) * 0.1 = 51 * 0.1 = 5.1
        // 5.1 > 5.0，所以应该返回 None
        assert_eq!(result, None);

        // 情况2: min_multiple 小于 qty
        let result = adjust_quantity(5.2, 5.1, 0.1);
        // 这应该返回 Some，因为 5.2 >= 5.1
        assert!(result.is_some());

        // 情况3: 测试 ceil 计算的精度问题
        let result = adjust_quantity(1.0, 1.01, 0.01);
        // min_multiple = ceil(1.01 / 0.01) * 0.01 = 101 * 0.01 = 1.01
        // 1.01 > 1.0，所以应该返回 None
        assert_eq!(result, None);
    }

    #[test]
    #[should_panic]
    fn test_compute_orders_invalid_ring_index() {
        setup_test_rates();
        // 测试无效的环索引
        let _result = ArbitrageEngine::compute_orders(9999);
        // 这应该会 panic 或返回 None，取决于实现
        // 由于使用了 unsafe 访问，这可能会导致 panic
    }

    #[test]
    fn test_compute_orders_no_rates() {
        setup_test_rates();
        // 测试没有设置汇率的情况
        let result = ArbitrageEngine::compute_orders(0);
        assert_eq!(result, None);
    }
}
